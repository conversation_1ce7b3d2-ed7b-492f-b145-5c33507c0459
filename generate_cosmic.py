import pandas as pd
import numpy as np

# 创建数据
def generate_cosmic_data(num_rows=250):
    data = []
    # 功能用户固定为'个人'
    function_users = ['个人'] * num_rows
    
    # 数据移动类型：一个输入对应一个输出，中间可以有多个读、写
    data_movement_types = []
    # 功能用户需求、触发事件、功能过程
    function_user_requirements = []
    trigger_events = []
    function_processes = []
    # 数据属性固定3-5个
    data_attributes_list = []
    
    # 定义一些可能的值
    inputs = ['系统配置数据输入', '告警信息输入', '性能数据输入', '日志数据输入', '用户操作输入']
    outputs = ['配置报告输出', '告警通知输出', '性能分析图表输出', '日志分析报告输出', '操作反馈输出']
    reads = ['读取数据库', '读取缓存', '读取配置文件', '读取日志文件', '读取实时数据流']
    writes = ['写入数据库', '写入缓存', '写入日志文件', '生成报表', '更新配置']
    requirements = ['系统监控', '故障诊断', '性能优化', '配置管理', '日志分析']
    triggers = ['定时执行', '事件触发', '用户请求', '系统启动', '异常检测']
    processes = ['数据收集', '数据处理', '数据分析', '报告生成', '告警触发']
    attributes = ['准确性', '完整性', '及时性', '安全性', '可访问性', '一致性', '可靠性']
    
    for i in range(num_rows):
        # 随机选择输入和输出
        input_type = np.random.choice(inputs)
        output_type = np.random.choice(outputs)
        
        # 随机生成中间的读、写操作（1-3个）
        num_reads = np.random.randint(1, 4)
        num_writes = np.random.randint(1, 4)
        read_operations = [np.random.choice(reads) for _ in range(num_reads)]
        write_operations = [np.random.choice(writes) for _ in range(num_writes)]
        
        # 构建数据移动类型
        data_movement = f'{input_type} -> ' + ' -> '.join(read_operations) + ' -> ' + ' -> '.join(write_operations) + f' -> {output_type}'
        data_movement_types.append(data_movement)
        
        # 功能用户需求
        req = np.random.choice(requirements)
        function_user_requirements.append(req)
        
        # 触发事件
        trigger = np.random.choice(triggers)
        trigger_events.append(trigger)
        
        # 功能过程（参考数据移动类型）
        process = f'{trigger} -> {req} -> ' + ' -> '.join(processes[:np.random.randint(2, 5)])
        function_processes.append(process)
        
        # 数据属性（3-5个）
        num_attrs = np.random.randint(3, 6)
        data_attrs = ', '.join(np.random.choice(attributes, num_attrs, replace=False))
        data_attributes_list.append(data_attrs)
    
    # 创建DataFrame，直接按照cosmic格式要求的列顺序
    # A列:功能用户, B列:功能用户需求, C列:触发事件, D列:功能过程
    # E列:空, F列:数据移动类型, G列:空, H列:数据属性
    df = pd.DataFrame({
        '功能用户': function_users,
        '功能用户需求': function_user_requirements,
        '触发事件': trigger_events,
        '功能过程': function_processes,
        '': [''] * num_rows,  # E列
        '数据移动类型': data_movement_types,
        '空列G': [''] * num_rows,  # G列
        '数据属性': data_attributes_list
    })
    
    return df

# 生成数据
cosmic_df = generate_cosmic_data(250)

# 保存到Excel
output_file = 'd:\\B\\集约化省侧运维支撑系统的方案_cosmic.xlsx'
cosmic_df.to_excel(output_file, index=False, header=True)

print(f'Cosmic文档已生成: {output_file}')